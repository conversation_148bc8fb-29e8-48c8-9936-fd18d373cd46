#include "keypad.h"
#include "state_machine_framework.h"
#include "pico/stdlib.h"
#include <stdio.h>

Keypad::Keypad() 
{
    SMF::subscribe(SYS_TICK_EVENT, this);
}

void Keypad::init()
{
    gpio_init(16); // KEY_S1
    gpio_set_dir(16, GPIO_IN);
    gpio_pull_up(16);

    gpio_init(17); // KEY_S2
    gpio_set_dir(17, GPIO_IN);
    gpio_pull_up(17);
}

void Keypad::handleEvent(Event event)
{    

    s1Shift = (s1Shift << 1) | gpio_get(16);
    s2Shift = (s2Shift << 1) | gpio_get(17);

    // Determine stable state
    bool s1Stable = (s1Shift == 0xFFFF); // Button released
    bool s2Stable = (s2Shift == 0xFFFF); // Button released
    bool s1Pressed = (s1Shift == 0x0000); // <PERSON><PERSON> pressed
    bool s2Pressed = (s2Shift == 0x0000); // <PERSON><PERSON> pressed 


        // Debouncing and edge detection for S1
    if (s1Pressed) {
        s1Counter++;
        if (s1Counter >= 150 && !s1LongSent) { // Long press detected (1.5s)
            SMF::postEvent(KEY_EVENT_LONG, KEY_S1);
            printf("KEY_EVENT_LONG S1\n");
            s1LongSent = true; // Prevent multiple long press events
        }
        s1LastState = true;
    } else {
        if (s1LastState && s1Counter < 150) { // Short press detected
            SMF::postEvent(KEY_EVENT, KEY_S1);
            printf("KEY_EVENT S1\n");
        }
        s1Counter = 0; // Reset counter
        s1LastState = false;
        s1LongSent = false; // Reset long press flag
    }

    // Debouncing and edge detection for S2
    if (s2Pressed) {
        s2Counter++;
        if (s2Counter >= 150 && !s2LongSent) { // Long press detected (1.5s)
            SMF::postEvent(KEY_EVENT_LONG, KEY_S2);
            printf("KEY_EVENT_LONG S2\n");
            s2LongSent = true; // Prevent multiple long press events
        }
        s2LastState = true;
    } else {
        if (s2LastState && s2Counter < 150) { // Short press detected
            SMF::postEvent(KEY_EVENT, KEY_S2);
            printf("KEY_EVENT S2\n");
        }
        s2Counter = 0; // Reset counter
        s2LastState = false;
        s2LongSent = false; // Reset long press flag
    }
}

