#include "blinking_led.h"
#include "pico/stdlib.h"

BlinkingLED::BlinkingLED() : state(ON)
{
    blinkTimer.set(50, this, BLINK_TIMER);
}

BlinkingLED::~BlinkingLED()
{
}

void BlinkingLED::init()
{
    gpio_init(6);
    gpio_set_dir(6, GPIO_OUT);
    gpio_put(6, true);
}

void BlinkingLED::handleEvent(Event evt)
{
    switch (state)
    {
    case OFF:
        if (evt.id == BLINK_TIMER) {
            blinkTimer.set(50, this, BLINK_TIMER);
            gpio_put(6, true);
            state = ON;
        }
        break;
    case ON:
        if (evt.id == BLINK_TIMER) {
            blinkTimer.set(50, this, BL<PERSON>K_TIMER);
            gpio_put(6, false);
            state = OFF;
        }
        break;
    }
}