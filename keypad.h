#ifndef KEYPAD_H
#define KEYPAD_H

#include <stdint.h>
#include "state_machine_framework.h"

class Keypad : public SM
{

public:
    Keypad();
    
    void init();
    void handleEvent(Event event);

    enum {KEY_S1 = 0, KEY_S2 = 1};

private:
    uint16_t s1Shift = 0xFFFF;
    uint16_t s2Shift = 0xFFFF;
    uint16_t s1Counter = 0;
    uint16_t s2Counter = 0;
    bool s1LastState = false;
    bool s2LastState = false;
    bool s1LongSent = false;
    bool s2LongSent = false;

    
};

#endif
